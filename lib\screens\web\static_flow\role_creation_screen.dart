import 'dart:developer';
import 'dart:math' as math;

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:nsl/models/RoleCreatModel.dart';
import 'package:nsl/models/add_role_model.dart';
import 'package:nsl/models/roles/get_roles_list.dart';
import 'package:nsl/models/roles/inheritance_role_model.dart';
import 'package:nsl/models/roles/validate_department_model.dart';
import 'package:nsl/models/roles_expansion_model.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';
import 'get_all_my_library_model.dart' hide Icon;
import 'package:nsl/providers/creation_provider.dart';
import 'package:nsl/utils/logger.dart';

// Tab data class for role creation tabs
class RoleTab {
  String id;
  String title;
  List<RoleRowData> roleRows;
  bool isValidated;

  RoleTab({
    required this.id,
    required this.title,
    required this.roleRows,
    this.isValidated = false,
  });
}

class RoleRowData {
  final TextEditingController roleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  String? selectedReportsTo;
  List<String> selectedItems = [];
  bool isValidated = false; // Track validation status for each role
  bool isAutoPopulated =
      false; // Track if this role was auto-populated from left panel
  bool isEditable = true;
  bool isAddedViaCreateOne = false; // Track if this row was added via "Create One" button
  // Error state tracking
  String? roleNameError;
  String? reportsToError;

  void clearErrors() {
    roleNameError = null;
    reportsToError = null;
  }

  void dispose() {
    roleController.dispose();
    descriptionController.dispose();
  }
}

class RoleCreationScreen extends StatefulWidget {
  final String? sessionId;
  final String? userIntent;

  const RoleCreationScreen({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<RoleCreationScreen> createState() => _RoleCreationScreenState();
}

class _RoleCreationScreenState extends State<RoleCreationScreen> {
  List<RoleRowData> roleRows = [];
  late AccordionController _accordionController;

  // Tab management
  List<RoleTab> roleTabs = [];
  int currentTabIndex = 0;

  List<Role> rolesList = [];

  List<Role> inheritanceList = [];
  List<String> selectedDepartments = [];
  bool _showOnlyDepartmentsContent = false;
  List<Role> departmentList = [];
  bool _isAIMode = true; // true for AI (right), false for Manual Process (left)

  @override
  void initState() {
    super.initState();
    print('=== DEBUGGING: initState called ===');
    print('=== DEBUGGING: roleRows.length before init: ${roleRows.length} ===');
    _accordionController = AccordionController();
    roleRows.add(RoleRowData());
    print('=== DEBUGGING: roleRows.length after adding initial role: ${roleRows.length} ===');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      fetchData();
      _autoPopulateRoleData();
      _addTextControllerListeners(); // Add listeners for initial row
    });
    _accordionController.addListener(() {
      setState(() {});
    });

    _initializeTabs();
  }

  void _initializeTabs() {
    // Create the first tab with initial role rows
    roleTabs.add(RoleTab(
      id: 'tab_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Extracted Details',
      roleRows: roleRows,
    ));
    currentTabIndex = 0;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print('=== DEBUGGING: didChangeDependencies called ===');
    // Check if we have role data to auto-populate
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Only auto-populate, don't check for trigger here (handled in Consumer)
      _autoPopulateRoleData();
    });
  }

  // Add listeners to text controllers to clear errors when user types
  void _addTextControllerListeners() {
    for (var rowData in roleRows) {
      rowData.roleController.addListener(() {
        if (rowData.roleNameError != null) {
          setState(() {
            rowData.roleNameError = null;
          });
        }
      });
    }
  }

  void fetchData() async {
    final rolesProvider =
        Provider.of<ObjectCreationProvider>(context, listen: false);
    //  await rolesProvider.fetchDepartment('departments');
    await rolesProvider.fetchRoles('roles');
    setState(() {});
  }

  /// Auto-populate role data if a role is pre-selected
  void _autoPopulateRoleData() {
    print('=== DEBUGGING: _autoPopulateRoleData called ===');

    // Try to get provider with different approaches
    CreationProvider? creationProvider;
    try {
      creationProvider = Provider.of<CreationProvider>(context, listen: false);
      print('=== DEBUGGING: Got provider using context ===');
    } catch (e) {
      print('=== DEBUGGING: Error getting provider: $e ===');
      return;
    }

    final role = creationProvider.prePopulatedRole;
    final allRoles = creationProvider.allRoles;

    print('Role from provider: ${role?.name}');
    print('Role ID from provider: ${role?.roleId}');
    print('All roles count: ${allRoles.length}');
    print('Current roleRows.length: ${roleRows.length}');

    if (role != null && currentTabRoleRows.isNotEmpty) {
      final firstRow = currentTabRoleRows[0];

      print('First row state: isAutoPopulated=${firstRow.isAutoPopulated}, text="${firstRow.roleController.text}"');

      // Don't auto-populate if the first role is already populated (to prevent overwriting user data)
      if (firstRow.isAutoPopulated || firstRow.roleController.text.isNotEmpty) {
        print('Skipping auto-population: First role already populated (isAutoPopulated: ${firstRow.isAutoPopulated}, text: "${firstRow.roleController.text}")');
        return;
      }

      print('Proceeding with auto-population for role: ${role.name}');

      print('Auto-populating role data for: ${role.name}');
      print('Role reports_to_role_id: ${role.reportsToRoleId}');

      // Populate role name
      firstRow.roleController.text = role.name ?? '';

      // Populate description
      firstRow.descriptionController.text = role.description ?? '';

      // Populate reports to dropdown
      if (role.reportsToRoleId != null && role.reportsToRoleId!.isNotEmpty) {
        Logger.info('Looking for role with ID: ${role.reportsToRoleId}');

        // Find the role name by role ID
        final reportsToRole = allRoles.firstWhere(
          (r) {
            Logger.debug('Checking role: ${r.name} with ID: ${r.roleId}');
            return r.roleId == role.reportsToRoleId;
          },
          orElse: () {
            Logger.warning(
                'No matching role found for ID: ${role.reportsToRoleId}');
            return RolesPostgre();
          },
        );

        if (reportsToRole.name != null && reportsToRole.name!.isNotEmpty) {
          firstRow.selectedReportsTo = reportsToRole.name;
          Logger.info('Set selectedReportsTo to: ${reportsToRole.name}');
        } else {
          Logger.warning('Reports to role name is null or empty');
        }
      } else {
        Logger.info('reports_to_role_id is null or empty');
      }

      // Mark this role as auto-populated
      firstRow.isAutoPopulated = true;
      print('Role marked as auto-populated');

      if (mounted) {
        setState(() {});
        print('Auto-population completed and setState called');
      } else {
        print('Widget not mounted, skipping setState');
      }
    } else {
      print('Cannot auto-populate: role is null or roleRows is empty');
      print('Role null: ${role == null}, roleRows empty: ${roleRows.isEmpty}');
    }
    print('=== END _autoPopulateRoleData ===');
  }

  /// Auto-populate role data using the provider data directly (called from Consumer)
  void _autoPopulateRoleDataFromProvider(CreationProvider creationProvider) {
    print('=== DEBUGGING: _autoPopulateRoleDataFromProvider called ===');
    final role = creationProvider.prePopulatedRole;
    final allRoles = creationProvider.allRoles;

    print('Role from provider: ${role?.name}');
    print('Role ID from provider: ${role?.roleId}');
    print('All roles count: ${allRoles.length}');
    print('Current roleRows.length: ${roleRows.length}');

    if (role != null && currentTabRoleRows.isNotEmpty) {
      final firstRow = currentTabRoleRows[0];

      print('First row state: isAutoPopulated=${firstRow.isAutoPopulated}, text="${firstRow.roleController.text}"');

      // Don't auto-populate if the first role is already populated (to prevent overwriting user data)
      if (firstRow.isAutoPopulated || firstRow.roleController.text.isNotEmpty) {
        print('Skipping auto-population: First role already populated (isAutoPopulated: ${firstRow.isAutoPopulated}, text: "${firstRow.roleController.text}")');
        return;
      }

      print('Proceeding with auto-population for role: ${role.name}');

      print('Auto-populating role data for: ${role.name}');
      print('Role reports_to_role_id: ${role.reportsToRoleId}');

      // Populate role name
      firstRow.roleController.text = role.name ?? '';

      // Populate reports-to if available
      if (role.reportsToRoleId != null && role.reportsToRoleId!.isNotEmpty) {
        final reportsToRole = allRoles.firstWhere(
          (r) {
            return r.roleId == role.reportsToRoleId;
          },
          orElse: () {
            Logger.warning(
                'No matching role found for ID: ${role.reportsToRoleId}');
            return RolesPostgre();
          },
        );

        if (reportsToRole.name != null && reportsToRole.name!.isNotEmpty) {
          firstRow.selectedReportsTo = reportsToRole.name;
          print('Reports-to set to: ${reportsToRole.name}');
        } else {
          Logger.info('reports_to_role_id is null or empty');
        }
      } else {
        Logger.info('reports_to_role_id is null or empty');
      }

      // Mark this role as auto-populated
      firstRow.isAutoPopulated = true;
      print('Role marked as auto-populated');

      if (mounted) {
        setState(() {});
        print('Auto-population completed and setState called');
      } else {
        print('Widget not mounted, skipping setState');
      }
    } else {
      print('Cannot auto-populate: role is null or roleRows is empty');
      print('Role null: ${role == null}, roleRows empty: ${roleRows.isEmpty}');
    }
    print('=== END _autoPopulateRoleDataFromProvider ===');
  }

  @override
  void dispose() {
    for (var row in roleRows) {
      row.dispose();
    }
    _accordionController.dispose();
    super.dispose();
  }

  void _addNewRow() {
    setState(() {
      roleRows.add(RoleRowData());
    });
    // Add listeners to the new row
    _addTextControllerListeners();
  }

  /// Check if the CreationProvider has triggered adding new role rows
  void _checkForNewRoleRowTrigger() async {
    final creationProvider = Provider.of<CreationProvider>(context, listen: false);
    print('=== DEBUGGING: _checkForNewRoleRowTrigger called ===');
    print('shouldAddNewRoleRow: ${creationProvider.shouldAddNewRoleRow}');

    if (creationProvider.shouldAddNewRoleRow) {
      print('Trigger detected! Adding 1 new role (3 UI rows)...');

      // Reset the trigger FIRST to prevent multiple calls
      creationProvider.resetAddNewRoleRowTrigger();
      print('Trigger reset. shouldAddNewRoleRow now: ${creationProvider.shouldAddNewRoleRow}');

      // Add 1 new role (which will render as 3 UI rows: Role+ReportsTo, Description, Validate)
      _addMultipleNewRows(1);

      print('=== FORCED setState() to rebuild UI ===');
    } else {
      print('No trigger detected.');
    }
    print('=== END DEBUGGING: _checkForNewRoleRowTrigger ===');
  }

  /// Create new tab instead of adding rows
  void _addMultipleNewRows(int count) {
    print('=== DEBUGGING: _addMultipleNewRows called - Creating new tab ===');

    // Create a new tab with a single role
    List<RoleRowData> newTabRoles = [RoleRowData()];

    RoleTab newTab = RoleTab(
      id: 'tab_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Extracted Details',
      roleRows: newTabRoles,
    );

    setState(() {
      roleTabs.add(newTab);
      currentTabIndex = roleTabs.length - 1; // Switch to the new tab
    });

    print('=== New tab created. Total tabs: ${roleTabs.length} ===');
    print('=== Switched to tab index: $currentTabIndex ===');
  }

  void _removeTab(int index) {
    if (roleTabs.length <= 1) return; // Don't remove the last tab

    setState(() {
      // Dispose of controllers in the tab being removed
      for (var roleRow in roleTabs[index].roleRows) {
        roleRow.dispose();
      }

      roleTabs.removeAt(index);

      // Adjust current tab index if necessary
      if (currentTabIndex >= roleTabs.length) {
        currentTabIndex = roleTabs.length - 1;
      } else if (currentTabIndex > index) {
        currentTabIndex--;
      }
    });

    print('=== Tab removed. Total tabs: ${roleTabs.length} ===');
    print('=== Current tab index: $currentTabIndex ===');
  }

  // Helper method to get current tab's role rows
  List<RoleRowData> get currentTabRoleRows {
    if (roleTabs.isEmpty || currentTabIndex >= roleTabs.length) {
      return roleRows; // Fallback to global roleRows
    }
    return roleTabs[currentTabIndex].roleRows;
  }

  // Method to publish all validated roles across all tabs
  Future<void> _publishAllValidatedRoles(ObjectCreationProvider provider) async {
    print('=== DEBUGGING: _publishAllValidatedRoles called ===');

    // Collect all validated roles from all tabs
    List<RoleRowData> allValidatedRoles = [];

    for (int tabIndex = 0; tabIndex < roleTabs.length; tabIndex++) {
      final tab = roleTabs[tabIndex];
      for (int roleIndex = 0; roleIndex < tab.roleRows.length; roleIndex++) {
        final role = tab.roleRows[roleIndex];
        if (role.isValidated) {
          allValidatedRoles.add(role);
          print('Added validated role: ${role.roleController.text} from Tab ${tabIndex + 1}');
        }
      }
    }

    print('Total validated roles to publish: ${allValidatedRoles.length}');

    // Publish each validated role
    for (int i = 0; i < allValidatedRoles.length; i++) {
      final role = allValidatedRoles[i];
      print('Publishing role ${i + 1}/${allValidatedRoles.length}: ${role.roleController.text}');

      try {
        if (role.isAutoPopulated) {
          await provider.publishRole(
              provider.validateRoleModel?.parsedData?.roleId ?? "",
              isEditMode: true);
        } else {
          await provider.publishRole(
              provider.validateRoleModel?.parsedData?.roleId ?? "");
        }

        print('Successfully published role: ${role.roleController.text}');
      } catch (e) {
        print('Error publishing role ${role.roleController.text}: $e');
        // Continue with next role even if one fails
      }
    }

    print('=== END _publishAllValidatedRoles ===');
  }

  void _removeRow(int index) {
    if (index > 0 && index < roleRows.length) {
      setState(() {
        roleRows[index].dispose();
        roleRows.removeAt(index);
      });
    }
  }

  /// Remove a single role that was added via "Create One"
  void _removeRole(int index) {
    print('=== DEBUGGING: _removeRole called for index $index ===');
    if (index > 0 && index < roleRows.length && roleRows[index].isAddedViaCreateOne) {
      print('=== Removing role at index $index ===');
      setState(() {
        roleRows[index].dispose();
        roleRows.removeAt(index);
      });
      print('=== Role removed. New roleRows.length: ${roleRows.length} ===');
    } else {
      print('=== Cannot remove: Invalid index or not added via Create One ===');
    }
  }

  /// Check if this is the first row of a "Create One" set
  bool _isFirstRowOfCreateOneSet(int index) {
    print('=== DEBUGGING: _isFirstRowOfCreateOneSet called for index $index ===');
    print('=== roleRows[$index].isAddedViaCreateOne: ${roleRows[index].isAddedViaCreateOne} ===');

    if (index == 0 || !roleRows[index].isAddedViaCreateOne) {
      print('=== Returning false: index is 0 or not added via Create One ===');
      return false;
    }

    // Check if the previous row is NOT from "Create One" or if this is the start of a new set
    if (index > 0 && !roleRows[index - 1].isAddedViaCreateOne) {
      print('=== Returning true: Previous row is not from Create One ===');
      return true; // This is the first "Create One" row after regular rows
    }

    // For consecutive "Create One" rows, check if this starts a new set of 3
    int createOneCount = 0;
    for (int i = index - 1; i >= 0; i--) {
      if (!roleRows[i].isAddedViaCreateOne) {
        break; // Found the boundary
      }
      createOneCount++;
    }

    print('=== createOneCount before this index: $createOneCount ===');
    print('=== createOneCount % 3 == 0: ${createOneCount % 3 == 0} ===');

    // If we have a multiple of 3 "Create One" rows before this one, this starts a new set
    bool result = createOneCount % 3 == 0;
    print('=== Returning $result for index $index ===');
    return result;
  }

  /// Remove a set of 3 role rows that were added via "Create One"
  void _removeCreateOneRoleSet(int startIndex) {
    setState(() {
      // Remove 3 consecutive rows starting from startIndex
      for (int i = 0; i < 3 && startIndex < roleRows.length; i++) {
        roleRows[startIndex].dispose();
        roleRows.removeAt(startIndex);
      }
    });
  }

  // Method to clear all fields after successful publish
  void _clearAllFields() async {
    setState(() {
      // Clear all role rows and dispose controllers
      for (var row in roleRows) {
        row.dispose();
      }
      roleRows.clear();

      // Add a fresh empty row
      roleRows.add(RoleRowData());

      // Clear other lists
      rolesList.clear();
      inheritanceList.clear();
      departmentList.clear();
      selectedDepartments.clear();

      // Reset UI state
      _showOnlyDepartmentsContent = false;
      fetchData();
    });

    // Clear provider data
    final provider =
        Provider.of<ObjectCreationProvider>(context, listen: false);
    provider.validateRoleModel = null;
    provider.saveRoleModel = null;
    provider.validateInheritanceModel = null;
    provider.validateDepartmentModel = null;
    provider.saveValidDepartmentModel = null;
    provider.publishEntityModel = null;

    // Refresh roles data to include newly published roles
    await provider.fetchRoles('roles');
  }

  // Method to validate and save role data
  Future<void> _validateAndSaveRole(int index) async {
    print('=== DEBUGGING: _validateAndSaveRole called for index $index ===');

    final provider =
        Provider.of<ObjectCreationProvider>(context, listen: false);

    // Get data from the specific role row in current tab
    RoleRowData rowData = currentTabRoleRows[index];

    // Clear previous errors
    rowData.clearErrors();
    bool hasErrors = false;

    // Validate role name
    if (rowData.roleController.text.isEmpty) {
      rowData.roleNameError = 'Role name cannot be empty';
      hasErrors = true;
    }

    // else if (!RegExp(r'^[a-zA-Z0-9_]+$')
    //     .hasMatch(rowData.roleController.text)) {
    //   rowData.roleNameError =
    //       'Role name can only contain letters, numbers, and underscores';
    //   hasErrors = true;
    // }

    // Validate reports to dropdown
    if ((rowData.selectedReportsTo ?? "").isEmpty
        || rowData.selectedReportsTo == 'None'
        ) {
       rowData.reportsToError = 'Please select a report to role';
      hasErrors = true;
    }

    // If there are validation errors, update UI and return
    if (hasErrors) {
      setState(() {});
      return;
    }

    // Create role list from current role data
    List<RoleCreateModel> roleList = [];

    // Create role model for validation
    RoleCreateModel roleModel = RoleCreateModel(
        roleName: rowData.roleController.text,
        description: rowData.descriptionController.text,
        reportsTo: rowData.selectedReportsTo ?? '',
        department: '', // Default department or get from context
        orgLevel: '', // Default org level or get from context
        inherits: "" //rowData.selectedItems.join(', '),
        );

    print('Role data being sent to API:');
    print('  - Role Name: ${roleModel.roleName}');
    print('  - Description: ${roleModel.description}');
    print('  - Reports To: ${roleModel.reportsTo}');
    print('  - Department: ${roleModel.department}');
    print('  - Org Level: ${roleModel.orgLevel}');
    print('  - Inherits: ${roleModel.inherits}');

    roleList.add(roleModel);

    try {
      // Check if this role was auto-populated to determine edit mode
      bool isEditMode = rowData.isAutoPopulated || !(rowData.isEditable);

      // Add mode - validate multiple roles
      await provider.parseValidateAddRoleEntity(roleList,
          isEditMode: isEditMode);

      if (provider.validateRoleModel?.hasErrors == true) {
        await showDialog(
          context: context,
          builder: (context) => roleValidationErrorDialog(
              context, "Role Configuration", provider.validateRoleModel),
        );
      } else {
        // If validation passes, mark role as validated and add to the list
        if (provider.saveRoleModel?.success == true) {
          setState(() {
            rowData.isValidated = true;
            rowData.isEditable = false;

            rolesList.add(Role(
              roleName: roleModel.roleName,
              roleConfiguration: RoleConfiguration(
                roleName: roleModel.roleName,
                description: roleModel.description,
                reportsTo: roleModel.reportsTo,
                organizationLevel: "",
                department: "",
                inherits: roleModel.inherits,
              ),
              departmentConfiguration: DepartmentConfiguration(
                departmentName: roleModel.department,
                description: roleModel.description,
                departmentHeadRole: roleModel.department,
              ),
              roleInheritance: RoleInheritance(
                parentRole: roleModel.roleName,
                inheritsRole: roleModel.inherits,
              ),
            ));
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Role validated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          // Show success message or feedback
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to save Role'),
              backgroundColor: Colors.red,
            ),
          );
          // Show success message or feedback
          // ScaffoldMessenger.of(context).showSnackBar(
          //   SnackBar(
          //     content: Text(provider.saveRoleModel?),
          //     backgroundColor: Colors.green,
          //   ),
          // );
        }

        setState(() {
          // Trigger UI update
        });
      }
    } catch (e) {
      // Handle any errors during validation
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error validating role: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  int _calculateTotalWidgets() {
    int totalWidgets = 0;

    if (_showOnlyDepartmentsContent) {
      // When showing departments content
      totalWidgets += 1; // Condensed role row
    } else {
      // When showing full role rows
      for (int i = 0; i < roleRows.length; i++) {
        totalWidgets += 2; // Role field + Description field
        if (i > 0) {
          totalWidgets += 1; // Remove button for additional rows
        }
      }
      totalWidgets += 1; // Add button
      // Removed: Object details section (commented out in UI)
    }

    return totalWidgets;
  }

  @override
  Widget build(BuildContext context) {
    int currentRowNumber = 1;

    // Debug: Print current state
    print('=== BUILD METHOD: roleRows.length = ${roleRows.length} ===');
    print('=== BUILD METHOD: _showOnlyDepartmentsContent = $_showOnlyDepartmentsContent ===');

    return Consumer<CreationProvider>(
      builder: (context, creationProvider, _) {
        // Debug provider state every time consumer builds
        print('=== CONSUMER BUILD: shouldAddNewRoleRow = ${creationProvider.shouldAddNewRoleRow} ===');
        print('=== CONSUMER BUILD: prePopulatedRole = ${creationProvider.prePopulatedRole?.name} ===');
        print('=== CONSUMER BUILD: allRoles count = ${creationProvider.allRoles.length} ===');

        // Check for new role row trigger whenever provider changes
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _checkForNewRoleRowTrigger();
          // Also check for auto-population with the current provider data
          _autoPopulateRoleDataFromProvider(creationProvider);
        });

        return Consumer<ObjectCreationProvider>(
          key: ValueKey('role_creation_${roleRows.length}'), // Force rebuild when roleRows changes
          builder: (context, dataProvide, _) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            // Header widget spanning full width

            _buildTabBar(),
            // Content area with sidebar and main content
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left sidebar with numbering
                  Container(
                    width: 40,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        right: BorderSide(
                          color: Colors.grey[300]!,
                          width: 1.0,
                        ),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 0),
                        child: Column(
                          children: _buildNumberedRows(),
                        ),
                      ),
                    ),
                  ),
                  // Main content area
                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            // Show condensed role view when departments content is shown
                            if (_showOnlyDepartmentsContent)
                              _buildCondensedRoleRow(currentRowNumber++),

                            // Show full role rows when not showing departments content
                            if (!_showOnlyDepartmentsContent) ...[
                              // Debug current state
                              () {
                                print('=== UI BUILD: roleRows.length = ${roleRows.length} ===');
                                return Container(height: 0); // Invisible debug widget
                              }(),
                              // Generate widgets for each role using indexed loop instead of map
                              ...List.generate(currentTabRoleRows.length, (index) {
                                RoleRowData rowData = currentTabRoleRows[index];

                                try {

                                  print('=== UI RENDERING: Processing role $index (${index + 1}/${currentTabRoleRows.length}) ===');

                                  int roleRowNumber = currentRowNumber++;
                                  int descriptionRowNumber = currentRowNumber++;
                                  int? removeRowNumber;
                                  if (index > 0) {
                                    removeRowNumber = currentRowNumber++;
                                  }

                                  print('=== UI RENDERING: About to return Column for role $index ===');

                                  final column = Column(
                                  children: [
                                    _buildFormRow(
                                      rowNumber: roleRowNumber,
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 3,
                                            child: _buildEditableFormField(
                                              context,
                                              "Enter Role",
                                              "Role",
                                              rowData.roleController,
                                              errorMessage:
                                                  rowData.roleNameError,
                                            ),
                                          ),
                                          const SizedBox(width: 20),
                                          Expanded(
                                            flex: 2,
                                            child: _buildEditableDropdownField(
                                                context,
                                                dataProvide,
                                                'Reports To',
                                                rowData.selectedReportsTo,
                                                (value) {
                                              setState(() {
                                                rowData.selectedReportsTo =
                                                    value;
                                                rowData.reportsToError = null;
                                                log(rowData.selectedReportsTo
                                                    .toString());
                                              });
                                            },
                                                errorMessage:
                                                    rowData.reportsToError),
                                          ),
                                        ],
                                      ),
                                      isRoleRow: true,
                                    ),
                                    _buildFormRow(
                                      rowNumber: descriptionRowNumber,
                                      child: !rowData.isValidated
                                          ? _buildEditableFormField(
                                              context,
                                              "Enter Description",
                                              "Description",
                                              rowData.descriptionController,
                                            )
                                          : Container(), // Empty container when validated
                                    ),
                                    if (!rowData.isValidated) ...[
                                      // Only show validate button if not validated
                                      SizedBox(height: AppSpacing.xxs),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          // Show Remove button for all role sets added via "Create One"
                                          if (rowData.isAddedViaCreateOne) ...[
                                            InkWell(
                                              onTap: () => _removeRole(index),
                                              child: Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 8, vertical: 5),
                                                decoration: BoxDecoration(
                                                  color: Colors.red[50],
                                                  border: Border.all(
                                                      color: Colors.red[300]!,
                                                      width: 0.5),
                                                ),
                                                child: Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    Icon(
                                                      Icons.delete_outline,
                                                      color: Colors.red[600],
                                                      size: 12,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      'Remove',
                                                      style: FontManager.getCustomStyle(
                                                        fontSize: 8,
                                                        fontWeight: FontWeight.w400,
                                                        fontFamily: FontManager
                                                            .fontFamilyTiemposText,
                                                        color: Colors.red[600]!,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 10),
                                          ],
                                          InkWell(
                                            onTap: () =>
                                                _validateAndSaveRole(index),
                                            child: Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 10, vertical: 7),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                border: Border.all(
                                                    color: Color(0xFF707070),
                                                    width: 0.5),
                                              ),
                                              child: Text(
                                                'Validate',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: 9,
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                    // if (index > 0)
                                    //   _buildFormRow(
                                    //     rowNumber: removeRowNumber!,
                                    //     child: Row(
                                    //       children: [
                                    //         Expanded(child: Container()),
                                    //         GestureDetector(
                                    //           onTap: () => _removeRow(index),
                                    //           child: Container(
                                    //             padding:
                                    //                 const EdgeInsets.symmetric(
                                    //                     horizontal: 10,
                                    //                     vertical: 6),
                                    //             decoration: BoxDecoration(
                                    //               color: Colors.red[50],
                                    //               border: Border.all(
                                    //                   color: Colors.red[300]!),
                                    //               borderRadius:
                                    //                   BorderRadius.circular(4),
                                    //             ),
                                    //             child: Row(
                                    //               mainAxisSize:
                                    //                   MainAxisSize.min,
                                    //               children: [
                                    //                 Icon(
                                    //                   Icons.delete_outline,
                                    //                   color: Colors.red[600],
                                    //                   size: 16,
                                    //                 ),
                                    //                 const SizedBox(width: 4),
                                    //                 Text(
                                    //                   'Remove',
                                    //                   style: TextStyle(
                                    //                     color: Colors.red[600],
                                    //                     fontSize: 10,
                                    //                     fontWeight:
                                    //                         FontWeight.w500,
                                    //                   ),
                                    //                 ),
                                    //               ],
                                    //             ),
                                    //           ),
                                    //         ),
                                    //       ],
                                    //     ),
                                    //   ),
                                    // SizedBox(height: AppSpacing.xxs),
                                    // Row(
                                    //   mainAxisAlignment: MainAxisAlignment.end,
                                    //   children: [
                                    //     InkWell(
                                    //       onTap: () =>
                                    //           _validateAndSaveRole(index),
                                    //       child: Container(
                                    //           padding: EdgeInsets.symmetric(
                                    //               horizontal: 10, vertical: 7),
                                    //           decoration: BoxDecoration(
                                    //               color: Colors.white,
                                    //               border: Border.all(
                                    //                   color: Color(0xFF707070),
                                    //                   width: 0.5)),
                                    //           child: Text(
                                    //             'Validate',
                                    //             style:
                                    //                 FontManager.getCustomStyle(
                                    //               fontSize: 9,
                                    //               fontWeight: FontWeight.w400,
                                    //               fontFamily: FontManager
                                    //                   .fontFamilyTiemposText,
                                    //               color: Colors.black,
                                    //             ),
                                    //           )),
                                    //     ),
                                    //   ],
                                    // ),
                                  ],
                                );

                                print('=== UI RENDERING: Column for role $index completed successfully ===');
                                return column;

                                } catch (e, stackTrace) {
                                  print('=== ERROR: Exception in role $index rendering: $e ===');
                                  print('=== ERROR: Stack trace: $stackTrace ===');
                                  // Return a simple error widget to prevent the entire list from failing
                                  return Container(
                                    height: 100,
                                    color: Colors.red[100],
                                    child: Center(
                                      child: Text('Error rendering role $index: $e'),
                                    ),
                                  );
                                }
                              }).toList(),

                              // _buildFormRow(
                              //   rowNumber: currentRowNumber++,
                              //   child: Row(
                              //     children: [
                              //       Expanded(child: Container()),
                              //       GestureDetector(
                              //         onTap: _addNewRow,
                              //         child: Container(
                              //           width: 30,
                              //           height: 30,
                              //           decoration: BoxDecoration(
                              //             color: Colors.white,
                              //             border: Border.all(
                              //                 color: Colors.grey[300]!),
                              //             borderRadius:
                              //                 BorderRadius.circular(4),
                              //           ),
                              //           child: const Icon(
                              //             Icons.add,
                              //             color: Color(0xFF007AFF),
                              //             size: 20,
                              //           ),
                              //         ),
                              //       ),
                              //     ],
                              //   ),
                              // ),
                            ],

                            // Show departments content when requested
                            // if (_showOnlyDepartmentsContent)
                            //   Container(
                            //     decoration: const BoxDecoration(
                            //       border: Border(
                            //         top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                            //       ),
                            //       color: Colors.white,
                            //     ),
                            //     child: _buildDepartmentsContent(context),
                            //   ),

                            // // Show expansion tile when not showing departments content
                            // if (!_showOnlyDepartmentsContent)
                            //   _buildObjectDetailsSection(context, 'Role Configuration'),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Bottom UI with Publish button - shows when validation and save are successful
            if (dataProvide.validateRoleModel != null &&
                dataProvide.validateRoleModel?.hasErrors == false &&
                dataProvide.saveRoleModel != null)
              _buildBottomPublishBar(context, dataProvide),
          ],
        ),
      );
    });
      },
    );
  }

  Widget _buildBottomPublishBar(
      BuildContext context, ObjectCreationProvider provider) {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey[300]!,
            width: 1.0,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () async {
              // Check if all roles across ALL tabs are validated before publishing
              List<String> unvalidatedRoleNames = [];

              print('=== DEBUGGING: Checking validation status across all tabs ===');

              for (int tabIndex = 0; tabIndex < roleTabs.length; tabIndex++) {
                final tab = roleTabs[tabIndex];
                print('Tab ${tabIndex + 1}: ${tab.roleRows.length} roles');

                for (int roleIndex = 0; roleIndex < tab.roleRows.length; roleIndex++) {
                  final role = tab.roleRows[roleIndex];
                  String roleName = role.roleController.text.isNotEmpty
                      ? role.roleController.text
                      : 'Unnamed Role';

                  print('  Role: $roleName - isValidated: ${role.isValidated}');

                  if (!role.isValidated) {
                    String tabName = 'Tab ${tabIndex + 1}';
                    unvalidatedRoleNames.add('$roleName (in $tabName)');
                    print('  ❌ Added to unvalidated list: $roleName (in $tabName)');
                  } else {
                    print('  ✅ Role is validated: $roleName');
                  }
                }
              }

              print('Total unvalidated roles: ${unvalidatedRoleNames.length}');
              print('Unvalidated roles: $unvalidatedRoleNames');

              if (unvalidatedRoleNames.isNotEmpty) {
                // Show specific error message with role names
                String errorMessage = unvalidatedRoleNames.length == 1
                    ? 'Please validate the role: ${unvalidatedRoleNames.first}'
                    : 'Please validate the following roles:\n${unvalidatedRoleNames.join('\n')}';

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(errorMessage),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 5),
                  ),
                );
                return;
              }

              // All roles are validated, proceed with publishing all roles
              await _publishAllValidatedRoles(provider);

              if (provider.publishEntitySuccessModel?.status == "success") {
                // Clear all related fields after successful publish
                _clearAllFields();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Role published successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Failed to publish role. Please try again.'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF007AFF),
              foregroundColor: Colors.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              minimumSize: const Size(100, 40),
            ),
            child: Text(
              'Publish',
              style: FontManager.getCustomStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // Tab buttons
            for (int i = 0; i < roleTabs.length; i++)
              _buildTabButton(i),
          ],
        ),
      ),
    );
  }

  Widget _buildTabButton(int index) {
    final tab = roleTabs[index];
    final isActive = index == currentTabIndex;

    // Get tab title from first role in the tab
    String tabTitle = 'Extracted Details';
    if (tab.roleRows.isNotEmpty) {
      final firstRow = tab.roleRows[0];
      if (firstRow.isValidated || firstRow.isAutoPopulated || firstRow.roleController.text.isNotEmpty) {
        tabTitle = firstRow.roleController.text.isNotEmpty
            ? firstRow.roleController.text
            : 'Extracted Details';
      }
    }

    return Container(
      margin: EdgeInsets.only(right: 2),
      child: InkWell(
        onTap: () {
          setState(() {
            currentTabIndex = index;
          });
        },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isActive ? Colors.white : Colors.grey[100],
            border: Border(
              bottom: BorderSide(
                color: isActive ? Colors.blue : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                tabTitle,
                style: FontManager.getCustomStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyInter,
                  color: Colors.black,
                ),
              ),
              // Close button (X icon) - only show if more than 1 tab
              if (roleTabs.length > 1)
                InkWell(
                  onTap: () => _removeTab(index),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 2, vertical: 1),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Colors.black),
                    ),
                    child: Icon(Icons.close, color: Colors.black, size: 12),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToggleSwitch() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isAIMode = !_isAIMode;
        });
      },
      child: Container(
        width: 44,
        height: 24,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12), color: Colors.white),
        child: Stack(
          children: [
            AnimatedPositioned(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              left: _isAIMode
                  ? 20
                  : 2, // Position based on mode: right for AI, left for Manual
              top: 2,
              child: Container(
                width: 20,
                height: 20,
                decoration: const BoxDecoration(
                  color: Color(0xFF3B82F6),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build numbered rows for the sidebar
  List<Widget> _buildNumberedRows() {
    int totalWidgets = _calculateTotalWidgets();
    return List.generate(totalWidgets, (index) {
      return Container(
        height: _getRowHeight(index),
        alignment: Alignment.center,
        decoration: BoxDecoration(),
        child: Container(
          width: 24,
          height: 20,
          alignment: Alignment.center,
          child: Text(
            '${index + 1}',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: 'SF Pro Text',
            ),
          ),
        ),
      );
    });
  }

  // Helper method to calculate row height based on content type
  double _getRowHeight(int index) {
    if (_showOnlyDepartmentsContent) {
      return 76.0; // Condensed role row height to match content
    } else {
      // Calculate based on role rows - reduced heights
      int currentIndex = 0;

      for (int i = 0; i < roleRows.length; i++) {
        if (currentIndex == index)
          return 55.0; // Role field row (reduced from 67px)
        currentIndex++;
        if (currentIndex == index)
          return 55.0; // Description field row (reduced from 67px)
        currentIndex++;
        if (i > 0) {
          if (currentIndex == index)
            return 45.0; // Remove button row (reduced from 56px)
          currentIndex++;
        }
      }

      if (currentIndex == index)
        return 45.0; // Add button row (reduced from 56px)
      currentIndex++;
      if (currentIndex == index)
        return 50.0; // Object details section (reduced from 60px)

      return 50.0; // Default height (reduced)
    }
  }

  Widget _buildCondensedRoleRow(int rowNumber) {
    return _buildFormRow(
      rowNumber: rowNumber,
      child: InkWell(
        onTap: () {
          setState(() {
            _showOnlyDepartmentsContent = false;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  currentTabRoleRows.isNotEmpty &&
                          currentTabRoleRows[0].roleController.text.isNotEmpty
                      ? "Role: ${currentTabRoleRows[0].roleController.text}"
                      : 'Role',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.add,
                  color: Color(0xFF007AFF),
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormRow({
    required int rowNumber,
    required Widget child,
    bool showNumbering = false,
    bool isRoleRow = false,
    bool isReportsToRow = false,
  }) {
    final rowData = currentTabRoleRows.isNotEmpty ? currentTabRoleRows[0] : null;
    final isFirstRowValidated = rowData?.isValidated ?? false;

    if (isFirstRowValidated && isRoleRow) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left side - Role (takes less space)
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  Text(
                    "Role:",
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: InkWell(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      onTap: () {
                        setState(() {
                          rowData!.isValidated = false;
                        });
                      },
                      child: Text(
                        rowData?.roleController.text ?? '',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 32), // Add some spacing between sections
            // Right side - Reports To (takes more space but starts from left)
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  Text(
                    "Reports To:",
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: InkWell(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      onTap: () {
                        setState(() {
                          rowData!.isValidated = false;
                        });
                      },
                      child: Text(
                        rowData?.selectedReportsTo ?? 'None',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: child,
    );
  }

  Widget _buildEditableFormField(BuildContext context, String hintText,
      String label, TextEditingController controller,
      {String? errorMessage, bool isEditable = true}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SizedBox(
              child: Text(
                "$label:",
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight:
                      label == "Role" ? FontWeight.w600 : FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.visible,
                maxLines: 2,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: SizedBox(
                height: 38,
                child: TextFormField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: hintText,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: errorMessage != null
                              ? Colors.red
                              : Colors.grey[400]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: errorMessage != null
                              ? Colors.red
                              : Colors.grey[400]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                          color: errorMessage != null
                              ? Colors.red
                              : const Color(0xFF0058FF)),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.red),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.red),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ],
        ),
        if (errorMessage != null) ...[
          const SizedBox(height: 4),
          Text(
            errorMessage,
            style: FontManager.getCustomStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.red,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildEditableDropdownField(
      BuildContext context,
      ObjectCreationProvider rolesProvide,
      String label,
      String? selectedValue,
      Function(String?) onChanged,
      {String? errorMessage,
      bool isEditable = true}) {
    // Get dynamic options from roles provider and ensure uniqueness
    Set<String> uniqueOptions = <String>{};
    if ((rolesProvide.getRolesListModel?.data?.postgresData ?? []).isNotEmpty) {
      for (PostgresDatum role
          in rolesProvide.getRolesListModel?.data?.postgresData ?? []) {
        if (role.name != null && role.name!.isNotEmpty) {
          uniqueOptions.add(role.name!);
        }
      }
    }

    // Convert to list for dropdown and add "None" option at the beginning
    List<String> dynamicOptions = ['None', ...uniqueOptions.toList()];

    // Validate selected value - keep it null if not in options or if options are empty
    String? validatedValue;
    if (selectedValue != null && dynamicOptions.contains(selectedValue)) {
      validatedValue = selectedValue;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          height: 38,
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                    color:
                        errorMessage != null ? Colors.red : Colors.grey[400]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                    color:
                        errorMessage != null ? Colors.red : Colors.grey[400]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                    color: errorMessage != null
                        ? Colors.red
                        : const Color(0xFF0058FF)),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              filled: true,
              fillColor: Colors.white,
            ),
            hint: Text(
              'Select $label',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[500],
              ),
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 24,
              ),
            ),
            iconSize: 24,
            isExpanded: true,
            value: validatedValue,
            onTap: () async {
              // Refresh roles data when dropdown is tapped
              await rolesProvide.fetchRoles('roles');
            },
            items: dynamicOptions.map((value) {
              return DropdownMenuItem(
                value: value,
                child: Container(
                  width: double.infinity,
                  child: Text(
                    value,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              );
            }).toList(),
            onChanged: onChanged,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ),
        if (errorMessage != null) ...[
          const SizedBox(height: 4),
          Text(
            errorMessage,
            style: FontManager.getCustomStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.red,
            ),
          ),
        ],
      ],
    );
  }

  Widget _multiSelectionDropDown(context, ObjectCreationProvider rolesProvide,
      String label, List<String> selectedItems) {
    // Get dynamic options from roles provider and ensure uniqueness
    Set<String> uniqueOptions = <String>{};
    if ((rolesProvide.getRolesListModel?.data?.postgresData ?? []).isNotEmpty) {
      for (PostgresDatum role
          in rolesProvide.getRolesListModel?.data?.postgresData ?? []) {
        if (role.name != null && role.name!.isNotEmpty) {
          uniqueOptions.add(role.name!);
        }
      }
    }
    // Convert to list for dropdown
    List<String> dynamicOptions = uniqueOptions.toList();
    return DropdownButtonHideUnderline(
      child: DropdownButton2<String>(
        isExpanded: true,
        customButton: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  selectedItems.isEmpty
                      ? 'Select Roles'
                      : selectedItems.join(', '),
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        selectedItems.isEmpty ? Colors.grey[500] : Colors.black,
                    overflow: TextOverflow.ellipsis,
                  ),
                  maxLines: 1,
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                size: 20,
                color: Colors.grey[600],
              ),
            ],
          ),
        ),
        value: null,
        onChanged: (_) {},
        items: dynamicOptions.map((item) {
          return DropdownMenuItem<String>(
            value: item,
            enabled: false,
            child: StatefulBuilder(
              builder: (context, menuSetState) {
                final isSelected = selectedItems.contains(item);
                return InkWell(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        selectedItems.remove(item);
                      } else {
                        selectedItems.add(item);
                      }
                    });
                    menuSetState(() {});
                  },
                  child: Container(
                    height: 44,
                    padding: const EdgeInsets.symmetric(horizontal: 12.0),
                    child: Row(
                      children: [
                        Icon(
                          isSelected
                              ? Icons.check_box
                              : Icons.check_box_outline_blank,
                          color: isSelected ? Colors.blue : Colors.grey[400],
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            item,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        }).toList(),
        buttonStyleData: const ButtonStyleData(
          padding: EdgeInsets.zero,
          width: double.infinity,
        ),
        menuItemStyleData: const MenuItemStyleData(
          padding: EdgeInsets.zero,
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(8)),
            boxShadow: [
              BoxShadow(
                color: Color(0x1A000000),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle) {
    bool isExpanded = true;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 1),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ListTileTheme(
          dense: true,
          child: ExpansionTile(
            initiallyExpanded: true,
            tilePadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
            childrenPadding: EdgeInsets.zero,
            trailing: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color:
                    isExpanded ? const Color(0xFF0058FF) : Colors.transparent,
              ),
              child: Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: isExpanded ? Colors.white : Colors.grey[600],
                size: 20,
              ),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    objectTitle,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                HoverBellIcon(
                  onTap: () {},
                ),
              ],
            ),
            children: [
              _buildObjectDetailsSection(context, objectTitle),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildObjectDetailsSection(BuildContext context, String objectTitle) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        children: [
          _buildSimpleAccordionItem(
            context,
            'Departments',
            'Completed',
            '25 Atributes',
            const Color(0xFFD1FAE5),
            const Color(0xFF065F46),
            onTap: () {
              setState(() {
                _showOnlyDepartmentsContent = true;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor, {
    VoidCallback? onTap,
  }) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              if (onTap != null) {
                onTap();
              } else {
                _accordionController.togglePanel('simple_$title');
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),
                        const SizedBox(width: 18),
                        Flexible(
                          flex: 10,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 150,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildDepartmentsContent(context),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDepartmentsContent(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(6),
                topRight: Radius.circular(6),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Text(
                      'Departments',
                      style: FontManager.getCustomStyle(
                        fontSize: 16,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFFD1FAE5),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Completed',
                        style: FontManager.getCustomStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: const Color(0xFF065F46),
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () => _showAddDepartmentModal(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF007AFF),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        minimumSize: const Size(0, 28),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.add,
                            size: 14,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Add Department',
                            style: FontManager.getCustomStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '25 Attributes',
                            style: FontManager.getCustomStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                          ),
                          // const SizedBox(width: 4),
                          // Icon(
                          //   Icons.keyboard_arrow_down,
                          //   size: 16,
                          //   color: Colors.grey[600],
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            height: 1,
            color: const Color(0xFFE5E7EB),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: const BoxDecoration(
              color: Color(0xFFF9FAFB),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT NAME',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    'DESCRIPTION',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'ROLES',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'DEPARTMENT HEAD ROLE',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'PARENT DEPARTMENT',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'ACTIONS',
                    style: FontManager.getCustomStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: departmentList.isEmpty
                ? const SizedBox.shrink()
                : ListView.builder(
                    itemCount: departmentList.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: Text(
                              departmentList[index]
                                      .departmentConfiguration
                                      ?.departmentName ??
                                  "",
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 4,
                            child: Text(
                              departmentList[index]
                                      .departmentConfiguration
                                      ?.description ??
                                  "",
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              "Role: ${currentTabRoleRows.isNotEmpty ? currentTabRoleRows[0].roleController.text : ''}",
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: Text(
                              departmentList[index]
                                      .departmentConfiguration
                                      ?.departmentHeadRole ??
                                  "",
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: Text(
                              departmentList[index]
                                      .departmentConfiguration
                                      ?.parentDepartment ??
                                  "",
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Row(
                              children: [
                                InkWell(
                                  onTap: () {},
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFFEF2F2),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Icon(
                                      Icons.delete_outline,
                                      size: 16,
                                      color: Color(0xFFEF4444),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

// add role modal popup
  void _showAddDepartmentModal(BuildContext context) {
    // Create local controllers for this modal
    final TextEditingController departmentNameController =
        TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    String? selectedDepartmentHead;
    String? selectedParentDepartment = 'None';
    List<String> selectedRoles = [];
    // List<String> selectedItems = [];
    List<DeptCreateModel> deptModelList = [];

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, buildState) {
            return Consumer2<ObjectCreationProvider, RolesProvider>(
                builder: (context, data, rolesProvide, _) {
              // Get dynamic options from roles provider and ensure uniqueness
              Set<String> uniqueRoleOptions = <String>{};
              if (rolesProvide.roles.isNotEmpty) {
                for (var role in rolesProvide.roles) {
                  if (role.name != null && role.name!.isNotEmpty) {
                    uniqueRoleOptions.add(role.name!);
                  }
                }
              }
              // Convert to list for dropdown
              List<String> dynamicRoleOptions = uniqueRoleOptions.toList();
              return Dialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Container(
                  width: 560,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x1A000000),
                        blurRadius: 16,
                        offset: Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Add Department',
                            style: FontManager.getCustomStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close),
                            iconSize: 20,
                            color: Colors.grey[600],
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Department Name Field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Department Name',
                            style: FontManager.getCustomStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            height: 40,
                            child: TextFormField(
                              controller: departmentNameController,
                              decoration: InputDecoration(
                                hintText: 'Enter Department Name',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF3B82F6)),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Description Field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Description',
                            style: FontManager.getCustomStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            height: 40,
                            child: TextFormField(
                              controller: descriptionController,
                              decoration: InputDecoration(
                                hintText: 'Enter Description',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF3B82F6)),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Select Roles and Department Head Role Row
                      Row(
                        children: [
                          // Select Roles
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Select Roles',
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                SizedBox(
                                  height: 40,
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton2<String>(
                                      isExpanded: true,
                                      customButton: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 0),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color: Colors.grey[300]!),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Colors.white,
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                selectedRoles.isEmpty
                                                    ? 'None'
                                                    : selectedRoles.join(', '),
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: selectedRoles.isEmpty
                                                      ? Colors.grey[500]
                                                      : Colors.black,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                maxLines: 1,
                                              ),
                                            ),
                                            Icon(
                                              Icons.keyboard_arrow_down,
                                              size: 20,
                                              color: Colors.grey[600],
                                            ),
                                          ],
                                        ),
                                      ),
                                      value: null,
                                      onChanged: (_) {},
                                      items: dynamicRoleOptions.map((item) {
                                        return DropdownMenuItem<String>(
                                          value: item,
                                          enabled: false,
                                          child: StatefulBuilder(
                                            builder: (context, menuSetState) {
                                              final isSelected =
                                                  selectedRoles.contains(item);
                                              return InkWell(
                                                onTap: () {
                                                  buildState(() {
                                                    if (isSelected) {
                                                      selectedRoles
                                                          .remove(item);
                                                    } else {
                                                      selectedRoles.add(item);
                                                    }
                                                  });
                                                  menuSetState(() {});
                                                },
                                                child: Container(
                                                  height: 44,
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12.0),
                                                  child: Row(
                                                    children: [
                                                      Icon(
                                                        isSelected
                                                            ? Icons.check_box
                                                            : Icons
                                                                .check_box_outline_blank,
                                                        color: isSelected
                                                            ? Colors.blue
                                                            : Colors.grey[400],
                                                      ),
                                                      const SizedBox(width: 12),
                                                      Expanded(
                                                        child: Text(
                                                          item,
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 14),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        );
                                      }).toList(),
                                      buttonStyleData: const ButtonStyleData(
                                        padding: EdgeInsets.zero,
                                        width: double.infinity,
                                      ),
                                      menuItemStyleData:
                                          const MenuItemStyleData(
                                        padding: EdgeInsets.zero,
                                      ),
                                      dropdownStyleData: DropdownStyleData(
                                        maxHeight: 200,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(8)),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Color(0x1A000000),
                                              blurRadius: 8,
                                              offset: Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Department Head Role
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Department Head Role',
                                  style: FontManager.getCustomStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                SizedBox(
                                  height: 40,
                                  child: DropdownButtonFormField<String>(
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                            color: Colors.grey[300]!),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                            color: Colors.grey[300]!),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: const BorderSide(
                                            color: Color(0xFF3B82F6)),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 12),
                                      filled: true,
                                      fillColor: Colors.white,
                                    ),
                                    hint: Text(
                                      'None',
                                      style: FontManager.getCustomStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily:
                                            FontManager.fontFamilyTiemposText,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                    icon: const Icon(Icons.keyboard_arrow_down,
                                        size: 20),
                                    isExpanded: true,
                                    value: selectedDepartmentHead,
                                    items: (['None'] + dynamicRoleOptions)
                                        .map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(
                                          value,
                                          style: FontManager.getCustomStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      buildState(() {
                                        selectedDepartmentHead = value;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Parent Department Field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Parent Department',
                            style: FontManager.getCustomStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            height: 40,
                            child: DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF3B82F6)),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              hint: Text(
                                'None',
                                style: FontManager.getCustomStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.grey[500],
                                ),
                              ),
                              icon: const Icon(Icons.keyboard_arrow_down,
                                  size: 20),
                              isExpanded: true,
                              value: selectedParentDepartment,
                              items: [
                                'None',
                                'Engineering',
                                'Sales',
                                'Product',
                                'Executive',
                                'Technology',
                                'Finance',
                                'Human Resources',
                              ].map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(
                                    value,
                                    style: FontManager.getCustomStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                buildState(() {
                                  selectedParentDepartment = value;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),

                      // Action Buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          OutlinedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(color: Color(0xFFD1D5DB)),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                              backgroundColor: Colors.white,
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: const Color(0xFF374151),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          ElevatedButton(
                            onPressed: () async {
                              deptModelList.add(DeptCreateModel(
                                  department: selectedParentDepartment ?? '',
                                  deptHeadRole: selectedDepartmentHead ?? '',
                                  deptName: departmentNameController.text,
                                  description: descriptionController.text,
                                  role: selectedRoles.join(', ')));
                              List<DeptCreateModel> temp = [];
                              for (int i = 0; i < deptModelList.length; i++) {
                                temp.add(DeptCreateModel(
                                    deptName: deptModelList[i].deptName,
                                    description: deptModelList[i].description,
                                    deptHeadRole: i == 0
                                        ? '[None]'
                                        : deptModelList[i].deptHeadRole,
                                    department: deptModelList[i].department,
                                    role: selectedRoles.join(', ')));
                              }
                              final provider =
                                  Provider.of<ObjectCreationProvider>(context,
                                      listen: false);
                              await provider.parseValidateDeptEntity(temp);

                              if (data.validateDepartmentModel?.isValid ==
                                  false) {
                                Navigator.of(context).pop();
                                await showDialog(
                                  context: context,
                                  builder: (context) =>
                                      departmentValidationErrorDialog(
                                          context,
                                          "Department",
                                          data.validateDepartmentModel),
                                );
                              } else {
                                if (data.saveValidDepartmentModel?.success ==
                                    true) {
                                  for (var param in deptModelList) {
                                    departmentList.add(Role(
                                        roleName: param.deptName,
                                        departmentConfiguration:
                                            DepartmentConfiguration(
                                                departmentName: param.deptName,
                                                description: param.description,
                                                departmentHeadRole:
                                                    param.deptHeadRole,
                                                parentDepartment:
                                                    param.department,
                                                role:
                                                    selectedRoles.join(', '))));
                                  }
                                  Navigator.of(context).pop();
                                  setState(() {});
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF3B82F6),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                            ),
                            child: Text(
                              'Apply This',
                              style: FontManager.getCustomStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            });
          },
        );
      },
    );
  }

  Widget departmentValidationErrorDialog(
      context, title, ValidateDepartmentModel? model) {
    // final provider =
    //     Provider.of<ObjectCreationProvider>(context, listen: false);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (model?.success != null)
                                Text(
                                  model?.failureReason ?? "",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if ((model?.dependencyErrors ?? []).isNotEmpty)
                                for (var element
                                    in model!.dependencyErrors ?? [])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.warnings != null)
                                for (var element
                                    in model?.issues?.warnings ?? [])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.exceptions != null)
                                for (var element
                                    in model?.issues?.exceptions ?? [])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.validationErrors != null)
                                for (var element
                                    in model?.issues?.validationErrors ?? [])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.dependencyErrors != null)
                                for (DependencyError element
                                    in model?.issues?.dependencyErrors ?? [])
                                  Text(
                                    "${element.message ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.uniquenessIssues != null)
                                for (var element
                                    in model?.issues?.uniquenessIssues ?? [])
                                  Text(
                                    "${element['message'] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.parsingIssues != null)
                                for (var element
                                    in model?.issues?.parsingIssues ?? [])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.mongoErrors != null)
                                for (var element
                                    in model?.issues?.mongoErrors ?? [])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.postgresErrors != null)
                                for (var element
                                    in model?.issues?.postgresErrors ?? [])
                                  Text(
                                    '${element["message"] ?? ''}\n',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.validationResult?.structureErrors !=
                                      null &&
                                  (model?.validationResult?.structureErrors ??
                                          [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.structureErrors ??
                                          [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult
                                          ?.requiredFieldErrors !=
                                      null &&
                                  (model?.validationResult
                                              ?.requiredFieldErrors ??
                                          [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult
                                              ?.requiredFieldErrors ??
                                          [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.dataTypeErrors !=
                                      null &&
                                  (model?.validationResult?.dataTypeErrors ??
                                          [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.dataTypeErrors ??
                                          [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.customErrors !=
                                      null &&
                                  (model?.validationResult?.customErrors ?? [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.customErrors ?? [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget roleValidationErrorDialog(
      context, title, ValidateAddRoleModel? model) {
    // final provider =
    //     Provider.of<ObjectCreationProvider>(context, listen: false);
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: math.min(MediaQuery.of(context).size.width * 0.8, 534),
        height: math.min(MediaQuery.of(context).size.height * 0.8, 600),
        child: Column(
          children: [
            // Header with bottom border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$title Validation Error',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyLarge(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyInter,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    iconSize: 24,
                    color: Colors.grey[600],
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            Expanded(
                child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24, vertical: 8.0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Purpose',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Text(
                      "We encountered one or more issues. Please review the following:",
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleSmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(
                      height: AppSpacing.xs,
                    ),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Color(0xffF8FAFC),
                          border: Border.all(color: Color(0xffE5E7EA)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: EdgeInsets.all(AppSpacing.sm),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            spacing: AppSpacing.xs,
                            children: [
                              if (model?.success != null)
                                Text(
                                  model?.failureReason ?? "",
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if ((model?.dependencyErrors ?? []).isNotEmpty)
                                for (var element
                                    in model!.dependencyErrors ?? [])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.warnings != null)
                                for (var element
                                    in model?.issues?.warnings ?? [])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.exceptions != null)
                                for (var element
                                    in model?.issues?.exceptions ?? [])
                                  Text(
                                    "${element ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.validationErrors != null)
                                for (var element
                                    in model?.issues?.validationErrors ?? [])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.dependencyErrors != null)
                                for (RoleDependencyError element
                                    in model?.issues?.dependencyErrors ?? [])
                                  Text(
                                    "${element.message ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.uniquenessIssues != null)
                                for (var element
                                    in model?.issues?.uniquenessIssues ?? [])
                                  Text(
                                    "${element['message'] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.parsingIssues != null)
                                for (var element
                                    in model?.issues?.parsingIssues ?? [])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.mongoErrors != null)
                                for (var element
                                    in model?.issues?.mongoErrors ?? [])
                                  Text(
                                    "${element["message"] ?? ''}\n",
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.issues?.postgresErrors != null)
                                for (var element
                                    in model?.issues?.postgresErrors ?? [])
                                  Text(
                                    '${element["message"] ?? ''}\n',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.titleSmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                              if (model?.validationResult?.structureErrors !=
                                      null &&
                                  (model?.validationResult?.structureErrors ??
                                          [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.structureErrors ??
                                          [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult
                                          ?.requiredFieldErrors !=
                                      null &&
                                  (model?.validationResult
                                              ?.requiredFieldErrors ??
                                          [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult
                                              ?.requiredFieldErrors ??
                                          [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.dataTypeErrors !=
                                      null &&
                                  (model?.validationResult?.dataTypeErrors ??
                                          [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.dataTypeErrors ??
                                          [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                              if (model?.validationResult?.customErrors !=
                                      null &&
                                  (model?.validationResult?.customErrors ?? [])
                                      .isNotEmpty)
                                Text(
                                  (model?.validationResult?.customErrors ?? [])
                                      .join('\n'),
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.titleSmall(context),
                                    fontWeight: FontWeight.w400,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
            )),
            // Footer with top border
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14, top: 14, left: 24, right: 24),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Proceed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Removed _isRoleDataUnchanged method - API will handle create vs update logic

  /// Show a snackbar with the given message and color
  void _showSnackBar(String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverBellIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            Icons.notifications_outlined,
            size: 18,
            color: isHovered ? Colors.red : Colors.grey[600],
          ),
        ),
      ),
    );
  }
}
